import { motion } from 'framer-motion';
import {
  ArrowR<PERSON>,
  Play,
  Star,
  Users,
  Zap,
  TrendingUp,
  BarChart3,
  Calendar,
  MessageSquare,
  Settings,
  Bell,
  Search,
  Filter,
  MoreHorizontal
} from 'lucide-react';

const DashboardLayout = () => {
  return (
    <section className="pt-20 pb-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Grid Layout */}
        <div className="grid grid-cols-12 gap-6 h-full">

          {/* Left Large Content Area */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="col-span-12 lg:col-span-6 space-y-6"
          >
            {/* Main Hero Card */}
            <div className="card p-8 h-80">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                </div>
                <MoreHorizontal className="w-5 h-5 text-gray-400" />
              </div>

              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-100 rounded w-1/2"></div>

                <div className="flex items-center space-x-4 mt-8">
                  <div className="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-gray-900">24.5K</div>
                    <div className="text-sm text-gray-500">Total Views</div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="h-3 bg-gray-100 rounded w-full"></div>
                  <div className="h-3 bg-primary-200 rounded w-2/3 -mt-3"></div>
                </div>
              </div>
            </div>

            {/* Bottom Row - Two Cards */}
            <div className="grid grid-cols-2 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="card p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <Calendar className="w-5 h-5 text-primary-600" />
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-100 rounded w-2/3"></div>
                  <div className="h-3 bg-gray-100 rounded w-1/3"></div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="card p-6"
              >
                <div className="flex items-center justify-between mb-4">
                  <BarChart3 className="w-5 h-5 text-purple-600" />
                  <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                </div>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-3 bg-gray-100 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-100 rounded w-2/3"></div>
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Center Content Area */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="col-span-12 lg:col-span-3 space-y-6"
          >
            {/* Top Card */}
            <div className="card p-6 h-48">
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <Search className="w-4 h-4 text-gray-400" />
              </div>
              <div className="h-3 bg-gray-100 rounded w-1/2 mb-3"></div>
              <div className="space-y-2">
                <div className="h-2 bg-gray-100 rounded w-full"></div>
                <div className="h-2 bg-gray-100 rounded w-3/4"></div>
                <div className="h-2 bg-gray-100 rounded w-1/2"></div>
              </div>
              <div className="mt-4">
                <div className="w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <Play className="w-4 h-4 text-primary-600" />
                </div>
              </div>
            </div>

            {/* Bottom Card */}
            <div className="card p-6 h-48">
              <div className="flex items-center justify-between mb-4">
                <MessageSquare className="w-5 h-5 text-green-600" />
                <Filter className="w-4 h-4 text-gray-400" />
              </div>
              <div className="space-y-3">
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-100 rounded w-1/2"></div>
                <div className="h-3 bg-gray-100 rounded w-2/3"></div>
                <div className="h-3 bg-gray-100 rounded w-1/3"></div>
                <div className="h-3 bg-gray-100 rounded w-3/4"></div>
              </div>
            </div>
          </motion.div>

          {/* Right Sidebar */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="col-span-12 lg:col-span-3 space-y-6"
          >
            {/* Top Widget */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <Bell className="w-4 h-4 text-orange-500" />
              </div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="h-3 bg-gray-100 rounded w-2/3"></div>
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
              </div>
              <div className="grid grid-cols-5 gap-2">
                <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                <div className="w-6 h-6 bg-gray-300 rounded-full"></div>
                <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
              </div>
            </div>

            {/* Middle Widget */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              </div>
              <div className="h-16 bg-gray-100 rounded mb-4"></div>
            </div>

            {/* Bottom Widget */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              </div>
              <div className="space-y-2 mb-4">
                <div className="h-3 bg-gray-100 rounded w-full"></div>
                <div className="h-3 bg-gray-100 rounded w-3/4"></div>
              </div>
              <div className="relative">
                <div className="absolute bottom-0 right-0 w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-tl-3xl"></div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default DashboardLayout;
