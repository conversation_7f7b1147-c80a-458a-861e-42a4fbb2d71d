import { motion } from 'framer-motion'
import { BarChart3, TrendingUp, Users, DollarSign, Activity, Calendar } from 'lucide-react'

const Dashboard = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="dashboard" className="py-20 relative">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
            Dashboard
            <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              {' '}Preview
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get a glimpse of what your dashboard could look like with our modern interface
          </p>
        </motion.div>

        {/* Dashboard Container */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="bg-white/5 backdrop-blur-md rounded-3xl p-8 border border-white/10"
        >
          {/* Top Navigation Bar */}
          <motion.div
            variants={itemVariants}
            className="flex items-center justify-between mb-8 pb-4 border-b border-white/10"
          >
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg"></div>
              <div className="flex space-x-2">
                <div className="w-16 h-6 bg-white/10 rounded-full"></div>
                <div className="w-12 h-6 bg-white/10 rounded-full"></div>
                <div className="w-14 h-6 bg-white/10 rounded-full"></div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-white/10 rounded-full"></div>
              <div className="w-8 h-8 bg-white/10 rounded-full"></div>
              <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-full"></div>
            </div>
          </motion.div>

          {/* Dashboard Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Main Chart */}
            <motion.div
              variants={itemVariants}
              className="lg:col-span-2 bg-white/5 rounded-2xl p-6 border border-white/10"
            >
              <div className="flex items-center justify-between mb-6">
                <div>
                  <div className="w-24 h-4 bg-white/20 rounded mb-2"></div>
                  <div className="w-16 h-3 bg-white/10 rounded"></div>
                </div>
                <BarChart3 className="h-6 w-6 text-purple-400" />
              </div>
              
              {/* Simulated Chart */}
              <div className="h-48 flex items-end space-x-2">
                {[40, 65, 45, 80, 55, 70, 60, 85, 50, 75, 90, 65].map((height, index) => (
                  <motion.div
                    key={index}
                    initial={{ height: 0 }}
                    whileInView={{ height: `${height}%` }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex-1 bg-gradient-to-t from-purple-500 to-pink-500 rounded-t-sm opacity-80"
                  />
                ))}
              </div>
              
              <div className="mt-4 flex justify-center">
                <div className="w-32 h-2 bg-white/10 rounded"></div>
              </div>
            </motion.div>

            {/* Right Column - Stats Cards */}
            <div className="space-y-6">
              {/* Stats Card 1 */}
              <motion.div
                variants={itemVariants}
                className="bg-white/5 rounded-2xl p-6 border border-white/10"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-20 h-4 bg-white/20 rounded"></div>
                  <TrendingUp className="h-5 w-5 text-green-400" />
                </div>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <div className="w-8 h-8 bg-purple-500/20 rounded-full"></div>
                    <div className="w-8 h-8 bg-pink-500/20 rounded-full"></div>
                    <div className="w-8 h-8 bg-blue-500/20 rounded-full"></div>
                    <div className="w-8 h-8 bg-green-500/20 rounded-full"></div>
                    <div className="w-8 h-8 bg-yellow-500/20 rounded-full"></div>
                  </div>
                  <div className="w-full h-2 bg-white/10 rounded"></div>
                  <div className="w-24 h-2 bg-white/10 rounded"></div>
                </div>
              </motion.div>

              {/* Stats Card 2 */}
              <motion.div
                variants={itemVariants}
                className="bg-white/5 rounded-2xl p-6 border border-white/10"
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="w-16 h-4 bg-white/20 rounded"></div>
                  <Users className="h-5 w-5 text-blue-400" />
                </div>
                <div className="w-full h-16 bg-white/5 rounded-lg mb-4"></div>
                <div className="w-full h-2 bg-white/10 rounded"></div>
              </motion.div>
            </div>
          </div>

          {/* Bottom Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
            {/* Bottom Card 1 */}
            <motion.div
              variants={itemVariants}
              className="bg-white/5 rounded-2xl p-6 border border-white/10"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-18 h-4 bg-white/20 rounded"></div>
                <DollarSign className="h-5 w-5 text-green-400" />
              </div>
              <div className="space-y-2">
                <div className="w-full h-2 bg-white/10 rounded"></div>
                <div className="w-3/4 h-2 bg-white/10 rounded"></div>
                <div className="w-1/2 h-2 bg-white/10 rounded"></div>
                <div className="w-2/3 h-2 bg-white/10 rounded"></div>
              </div>
            </motion.div>

            {/* Bottom Card 2 */}
            <motion.div
              variants={itemVariants}
              className="bg-white/5 rounded-2xl p-6 border border-white/10"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-20 h-4 bg-white/20 rounded"></div>
                <Activity className="h-5 w-5 text-purple-400" />
              </div>
              <div className="h-16 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-lg mb-4"></div>
              <div className="flex justify-between">
                <div className="w-12 h-2 bg-white/10 rounded"></div>
                <div className="w-8 h-2 bg-white/10 rounded"></div>
              </div>
            </motion.div>

            {/* Bottom Card 3 */}
            <motion.div
              variants={itemVariants}
              className="bg-white/5 rounded-2xl p-6 border border-white/10"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="w-16 h-4 bg-white/20 rounded"></div>
                <Calendar className="h-5 w-5 text-yellow-400" />
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <div className="w-16 h-3 bg-white/10 rounded"></div>
                  <div className="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg"></div>
                </div>
                <div className="w-full h-8 bg-white/5 rounded-lg"></div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
          >
            Try Dashboard Demo
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Dashboard
